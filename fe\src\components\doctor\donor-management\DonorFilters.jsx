import React from "react";
import { <PERSON>, <PERSON><PERSON>, <PERSON>, Col, Card } from "antd";
import { BLOOD_TYPES } from "../../../constants/systemConstants";

/**
 * Component bộ lọc người hiến máu
 */
const DonorFilters = ({ filters, setFilters, statistics, loading, onRefresh }) => {
  console.log("🎛️ DonorFilters received filters:", filters);
  const {
    todayCount,
    pendingCount,
    approvedCount,
    rejectedCount,
    cancelledCount
  } = statistics;

  // Các options cho filter trạng thái
  const statusOptions = [
    { value: "all", label: "Tất cả" },
    { value: "today", label: `Hôm nay (${todayCount})` },
    { value: "pending", label: `Ch<PERSON> duyệt (${pendingCount})` },
    { value: "approved", label: `Chấp nhận (${approvedCount})` },
    { value: "rejected", label: `<PERSON>hông chấp nhận (${rejectedCount})` },
    { value: "cancelled", label: `<PERSON><PERSON> hủy (${cancelledCount})` },
  ];

  // <PERSON>ác options cho filter nhóm máu
  const bloodTypeOptions = [
    { value: "all", label: "Tất cả nhóm máu" },
    ...BLOOD_TYPES.map(type => ({ value: type, label: type }))
  ];

  // Các options cho filter quy trình hiến máu
  const processOptions = [
    { value: "all", label: "Tất cả quy trình" },
    { value: 1, label: "Đăng ký" },
    { value: 2, label: "Khám sức khỏe cơ bản" },
    { value: 3, label: "Lấy máu" },
    { value: 4, label: "Xét nghiệm máu" },
    { value: 5, label: "Nhập kho" },
    { value: "rejected", label: "Không chấp nhận" },
  ];

  // Các options cho filter giờ hẹn
  const timeSlotOptions = [
    { value: "all", label: "Tất cả giờ hẹn" },
    { value: "morning", label: "Sáng (7:00 - 12:00)" },
    { value: "afternoon", label: "Chiều (13:00 - 17:00)" },
  ];

  return (
    <Card style={{ marginBottom: 16 }}>
      <Row gutter={[16, 16]} align="middle">
        <Col xs={24} sm={12} md={6}>
          <div style={{ marginBottom: 8 }}>
            <label style={{ fontWeight: "bold", color: "#20374E" }}>
              Trạng thái:
            </label>
          </div>
          <Select
            value={filters.status}
            onChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
            style={{ width: "100%" }}
            options={statusOptions}
          />
        </Col>

        <Col xs={24} sm={12} md={6}>
          <div style={{ marginBottom: 8 }}>
            <label style={{ fontWeight: "bold", color: "#20374E" }}>
              Nhóm máu:
            </label>
          </div>
          <Select
            value={filters.bloodType}
            onChange={(value) => setFilters(prev => ({ ...prev, bloodType: value }))}
            style={{ width: "100%" }}
            options={bloodTypeOptions}
          />
        </Col>

        <Col xs={24} sm={12} md={6}>
          <div style={{ marginBottom: 8 }}>
            <label style={{ fontWeight: "bold", color: "#20374E" }}>
              Quy trình:
            </label>
          </div>
          <Select
            value={filters.process}
            onChange={(value) => setFilters(prev => ({ ...prev, process: value }))}
            style={{ width: "100%" }}
            options={processOptions}
          />
        </Col>

        <Col xs={24} sm={12} md={6}>
          <div style={{ marginBottom: 8 }}>
            <label style={{ fontWeight: "bold", color: "#20374E" }}>
              Giờ hẹn:
            </label>
          </div>
          <Select
            value={filters.timeSlot}
            onChange={(value) => setFilters(prev => ({ ...prev, timeSlot: value }))}
            style={{ width: "100%" }}
            options={timeSlotOptions}
          />
        </Col>

        <Col xs={24} sm={24} md={24}>
          <Button
            onClick={onRefresh}
            loading={loading}
            type="primary"
            style={{ marginTop: 8 }}
          >
            Làm mới
          </Button>
        </Col>
      </Row>
    </Card>
  );
};

export default DonorFilters;
